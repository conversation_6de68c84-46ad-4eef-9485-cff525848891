<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $permissions = [
            'fees_collect_student_wise' => [
                'module' => null,
                'sidebar_menu' => 'system_settings',
                'name' => 'Fees Collect Student Wise',
                'lang_name' => 'Fees Collect Student Wise',
                'icon' => null,
                'svg' => null,
                'route' => 'fees_collect_student_wise',
                'parent_route' => 'collect_fees',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 3,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 0,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'section_id' => 1,
                'user_id' => null,
                'type' => 3,
                'old_id' => null,
                'child' => [],
            ],
            'update-general-settings-data' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Update General Settings Data',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'update-general-settings-data',
                'parent_route' => 'general-settings',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 409,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 409,
            ],
            'transport-route-edit' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Edit',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'transport-route-edit',
                'parent_route' => 'transport-route-index',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 352,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 351,
            ],
            'transport-route-delete' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'transport-route-delete',
                'parent_route' => 'transport-route-index',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 353,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 352,
            ],
            'vehicle-edit' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Edit',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'vehicle-edit',
                'parent_route' => 'vehicle-index',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 356,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 355,
            ],
            'vehicle-delete' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'vehicle-delete',
                'parent_route' => 'vehicle-index',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 357,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 356,
            ],
            'dormitory-list-store' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Store',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'dormitory-list-store',
                'parent_route' => 'dormitory-list-index',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 357,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 356,
            ],
            'exam_schedule_create' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Create Exam Schedule',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'exam_schedule_create',
                'parent_route' => 'exam_schedule',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 219,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 218,
            ],
            'staff-holiday-store' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Mark as holiday Staff',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'staff-holiday-store',
                'parent_route' => 'staff_attendance',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 219,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 218,
            ],

            'speech-slider-store' => [
                'module' => null,
                'sidebar_menu' => 'front_setting',
                'name' => 'Speech Slider Store',
                'lang_name' => 'front_settings.speech_slider',
                'icon' => null,
                'svg' => null,
                'route' => 'speech-slider-store',
                'parent_route' => 'speech-slider',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 8,
                'is_saas' => 0,
                'is_menu' => 1,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 493,
                'child' => [],
            ],
            'speech-slider-edit' => [
                'module' => null,
                'sidebar_menu' => 'front_setting',
                'name' => 'Speech Slider Edit',
                'lang_name' => 'front_settings.speech_slider',
                'icon' => null,
                'svg' => null,
                'route' => 'speech-slider-edit',
                'parent_route' => 'speech-slider',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 8,
                'is_saas' => 0,
                'is_menu' => 1,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 493,
                'child' => [],
            ],
            'speech-slider-update' => [
                'module' => null,
                'sidebar_menu' => 'front_setting',
                'name' => 'Speech Slider Update',
                'lang_name' => 'front_settings.speech_slider',
                'icon' => null,
                'svg' => null,
                'route' => 'speech-slider-update',
                'parent_route' => 'speech-slider',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 8,
                'is_saas' => 0,
                'is_menu' => 1,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 493,
                'child' => [],
            ],
            'speech-slider-delete-modal' => [
                'module' => null,
                'sidebar_menu' => 'front_setting',
                'name' => 'Speech Slider Delete Modal',
                'lang_name' => 'front_settings.speech_slider',
                'icon' => null,
                'svg' => null,
                'route' => 'speech-slider-delete-modal',
                'parent_route' => 'speech-slider',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 8,
                'is_saas' => 0,
                'is_menu' => 1,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 493,
                'child' => [],
            ],
            'speech-slider-delete' => [
                'module' => null,
                'sidebar_menu' => 'front_setting',
                'name' => 'Speech Slider Delete',
                'lang_name' => 'front_settings.speech_slider',
                'icon' => null,
                'svg' => null,
                'route' => 'speech-slider-delete',
                'parent_route' => 'speech-slider',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 8,
                'is_saas' => 0,
                'is_menu' => 1,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 493,
                'child' => [],
            ],

            'make-default-theme' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Make Default Theme',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'make-default-theme',
                'parent_route' => 'color-style',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 492,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 491,
            ],
            'theme-create' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Theme create',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'theme-create',
                'parent_route' => 'color-style',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 492,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 491,
            ],
            'theme-store' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'theme Store',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'theme-store',
                'parent_route' => 'color-style',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 492,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 491,
            ],
            'themes.copy' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'theme Copy',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'themes.copy',
                'parent_route' => 'color-style',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 492,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 491,
            ],
            // 'student_dormitory_report' => array(
            //     'module' => null,
            //     'sidebar_menu' => 'students_report',
            //     'name' => 'Student Dormitory Report',
            //     'lang_name' => 'reports.student_dormitory_report',
            //     'icon' => null,
            //     'svg' => null,
            //     'route' => 'student_dormitory_report',
            //     'parent_route' => 'students_report',
            //     'is_admin' => 1,
            //     'is_teacher' => 0,
            //     'is_student' => 0,
            //     'is_parent' => 0,
            //     'position' => 1,
            //     'is_saas' => 0,
            //     'is_menu' => 1,
            //     'status' => 1,
            //     'menu_status' => 1,
            //     'relate_to_child' => 0,
            //     'alternate_module' => null,
            //     'permission_section' => 0,
            //     'user_id' => null,
            //     'type' => 2,
            //     'old_id' => 538,
            // ),
            'student_transport_report' => [
                'module' => null,
                'sidebar_menu' => 'students_report',
                'name' => 'Student Transport Report',
                'lang_name' => 'reports.student_transport_report',
                'icon' => null,
                'svg' => null,
                'route' => 'student_transport_report',
                'parent_route' => 'students_report',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 1,
                'is_saas' => 0,
                'is_menu' => 1,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 2,
                'old_id' => 538,
            ],
            'save-exam-content' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Save Exam Settings',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'save-exam-content',
                'parent_route' => 'exam-settings',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'delete-content' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete Content',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'delete-content',
                'parent_route' => 'exam-settings',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'update-exam-content' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Update Exam Content',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'update-exam-content',
                'parent_route' => 'exam-settings',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'delete-holiday-data' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete Holiday',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'delete-holiday-data',
                'parent_route' => 'holiday',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'donor-store' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Store',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'donor-store',
                'parent_route' => 'donor',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'donor-edit' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Edit',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'donor-edit',
                'parent_route' => 'donor',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'donor-update' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Update',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'donor-update',
                'parent_route' => 'donor',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'donor-delete' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'donor-delete',
                'parent_route' => 'donor',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'donor-delete-modal' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete Modal',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'donor-delete-modal',
                'parent_route' => 'donor',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'form-download-store' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Store',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'form-download-store',
                'parent_route' => 'form-download',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'form-download-edit' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Store',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'form-download-edit',
                'parent_route' => 'form-download',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'form-download-update' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Update',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'form-download-update',
                'parent_route' => 'form-download',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'form-download-delete-modal' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete Modal',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'form-download-delete-modal',
                'parent_route' => 'form-download',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'form-download-delete' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'form-download-delete',
                'parent_route' => 'form-download',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'home-slider-store' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Store',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'home-slider-store',
                'parent_route' => 'home-slider',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'home-slider-edit' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Edit',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'home-slider-edit',
                'parent_route' => 'home-slider',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'home-slider-update' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Update',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'home-slider-update',
                'parent_route' => 'home-slider',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'home-slider-delete-modal' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete Modal',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'home-slider-delete-modal',
                'parent_route' => 'home-slider',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'home-slider-delete' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'home-slider-delete',
                'parent_route' => 'home-slider',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'expert-teacher-store' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Store',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'expert-teacher-store',
                'parent_route' => 'expert-teacher',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'expert-teacher-edit' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Edit',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'expert-teacher-edit',
                'parent_route' => 'expert-teacher',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'expert-teacher-update' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Update',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'expert-teacher-update',
                'parent_route' => 'expert-teacher',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'expert-teacher-delete-modal' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete Modal',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'expert-teacher-delete-modal',
                'parent_route' => 'expert-teacher',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'expert-teacher-delete' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'expert-teacher-delete',
                'parent_route' => 'expert-teacher',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'photo-gallery-store' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Store',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'photo-gallery-store',
                'parent_route' => 'photo-gallery',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'photo-gallery-edit' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Edit',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'photo-gallery-edit',
                'parent_route' => 'photo-gallery',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'photo-gallery-update' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Update',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'photo-gallery-update',
                'parent_route' => 'photo-gallery',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'photo-gallery-delete-modal' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete Modal',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'photo-gallery-delete-modal',
                'parent_route' => 'photo-gallery',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'photo-gallery-delete' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'photo-gallery-delete',
                'parent_route' => 'photo-gallery',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'photo-gallery-view-modal' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'View Photo',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'photo-gallery-view-modal',
                'parent_route' => 'photo-gallery',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'video-gallery-store' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Store',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'video-gallery-store',
                'parent_route' => 'video-gallery',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'video-gallery-edit' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Edit',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'video-gallery-edit',
                'parent_route' => 'video-gallery',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'video-gallery-update' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Update',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'video-gallery-update',
                'parent_route' => 'video-gallery',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'video-gallery-delete-modal' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete Modal',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'video-gallery-delete-modal',
                'parent_route' => 'video-gallery',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'video-gallery-delete' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'video-gallery-delete',
                'parent_route' => 'video-gallery',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'video-gallery-view-modal' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'View Video',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'video-gallery-view-modal',
                'parent_route' => 'video-gallery',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'front-result-store' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Store',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-result-store',
                'parent_route' => 'front-result',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'front-result-edit' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Edit',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-result-edit',
                'parent_route' => 'front-result',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'front-result-update' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Update',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-result-update',
                'parent_route' => 'front-result',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'front-result-delete-modal' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete Modal',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-result-delete-modal',
                'parent_route' => 'front-result',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'front-result-delete' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete Modal',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-result-delete',
                'parent_route' => 'front-result',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],

            'front-class-routine-store' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Store',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-class-routine-store',
                'parent_route' => 'front-class-routine',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'front-class-routine-edit' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Edit',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-class-routine-edit',
                'parent_route' => 'front-class-routine',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'front-class-routine-update' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Update',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-class-routine-update',
                'parent_route' => 'front-class-routine',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'front-class-routine-delete-modal' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete Modal',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-class-routine-delete-modal',
                'parent_route' => 'front-class-routine',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'front-class-routine-delete' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-class-routine-delete',
                'parent_route' => 'front-class-routine',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],

            'front-exam-routine-store' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Store',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-exam-routine-store',
                'parent_route' => 'front-exam-routine',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'front-exam-routine-edit' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Edit',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-exam-routine-edit',
                'parent_route' => 'front-exam-routine',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'front-exam-routine-update' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Update',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-exam-routine-update',
                'parent_route' => 'front-exam-routine',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'front-exam-routine-delete-modal' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete Modal',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-exam-routine-delete-modal',
                'parent_route' => 'front-exam-routine',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'front-exam-routine-delete' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-exam-routine-delete',
                'parent_route' => 'front-exam-routine',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'front-academic-calendar-store' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Store',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-academic-calendar-store',
                'parent_route' => 'front-academic-calendar',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'front-academic-calendar-edit' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Edit',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-academic-calendar-edit',
                'parent_route' => 'front-academic-calendar',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'front-academic-calendar-update' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Update',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-academic-calendar-update',
                'parent_route' => 'front-academic-calendar',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'front-academic-calendar-delete-modal' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete Modal',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-academic-calendar-delete-modal',
                'parent_route' => 'front-academic-calendar',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],
            'front-academic-calendar-delete' => [
                'module' => null,
                'sidebar_menu' => null,
                'name' => 'Delete',
                'lang_name' => null,
                'icon' => null,
                'svg' => null,
                'route' => 'front-academic-calendar-delete',
                'parent_route' => 'front-academic-calendar',
                'is_admin' => 1,
                'is_teacher' => 0,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 708,
                'is_saas' => 0,
                'is_menu' => 0,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 3,
                'old_id' => 707,
            ],

        ];
        foreach ($permissions as $permission) {
            storePermissionData($permission);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
