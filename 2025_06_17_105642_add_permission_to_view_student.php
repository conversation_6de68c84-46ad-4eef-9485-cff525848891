<?php

use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        
        $permissions = [
            [
                "name" => "view",
                "parent_route" => "student_list",
                "lang_name" =>    "common.view",
                "route" => "student_view",
                "status" => 1,
                "menu_status" => 1,
                "position" => 67,
                "is_saas" => 0,
                "relate_to_child" => 0,
                "is_menu" => 1,
                "is_admin" => 1,
                "type" => 3,
                "parent_id" => 0,
            ],
            [
                "name" => "preview",
                "parent_route" => "student-id-card",
                "lang_name" =>    "common.view",
                "route" => "student-id-card-preview",
                "status" => 1,
                "menu_status" => 1,
                "position" => 67,
                "is_saas" => 0,
                "relate_to_child" => 0,
                "is_menu" => 1,
                "is_admin" => 1,
                "type" => 3,
                "parent_id" => 0,
            ],
            [
                "name" => "preview",
                "parent_route" => "student-certificate",
                "lang_name" =>    "common.view",
                "route" => "student-certificate-view",
                "status" => 1,
                "menu_status" => 1,
                "position" => 67,
                "is_saas" => 0,
                "relate_to_child" => 0,
                "is_menu" => 1,
                "is_admin" => 1,
                "type" => 3,
                "parent_id" => 0,
            ],

             [
                "module" => "",
                "name" => "Add",
                "parent_route" => "vehicle-index",
                "lang_name" =>    "common.view",
                "route" => "vehicle-add",
                "status" => 1,
                "menu_status" => 1,
                "position" => 0,
                "is_saas" => 0,
                "relate_to_child" => 0,
                "is_menu" => 0,
                "is_admin" => 1,
                "type" => 3,
                "parent_id" => 0,
                "permission_section" => 0,
                "school_id" => 1,
            ],
            [
                "module" => "",
                "name" => "Add",
                "parent_route" => "transport-route-index",
                "lang_name" =>    "common.view",
                "route" => "transport-route-add",
                "status" => 1,
                "menu_status" => 1,
                "position" => 0,
                "is_saas" => 0,
                "relate_to_child" => 0,
                "is_menu" => 0,
                "is_admin" => 1,
                "type" => 3,
                "parent_id" => 0,
                "permission_section" => 0,
                "school_id" => 1,
            ],
            [
                "module" => "",
                "name" => "Add",
                "parent_route" => "assign-vehicle-index",
                "lang_name" =>    "common.add",
                "route" => "add-assign-vehicle",
                "status" => 1,
                "menu_status" => 1,
                "position" => 0,
                "is_saas" => 0,
                "relate_to_child" => 0,
                "is_menu" => 0,
                "is_admin" => 1,
                "type" => 3,
                "parent_id" => 0,
                "permission_section" => 0,
                "school_id" => 1,
            ],
            [
                "module" => "",
                "name" => "Edit",
                "parent_route" => "assign-vehicle-index",
                "lang_name" =>    "common.edit",
                "route" => "edit-assign-vehicle",
                "status" => 1,
                "menu_status" => 1,
                "position" => 0,
                "is_saas" => 0,
                "relate_to_child" => 0,
                "is_menu" => 0,
                "is_admin" => 1,
                "type" => 3,
                "parent_id" => 0,
                "permission_section" => 0,
                "school_id" => 1,
            ],
            [
                "module" => "",
                "name" => "Delete",
                "parent_route" => "assign-vehicle-index",
                "lang_name" =>    "common.delete",
                "route" => "delete-assign-vehicle",
                "status" => 1,
                "menu_status" => 1,
                "position" => 0,
                "is_saas" => 0,
                "relate_to_child" => 0,
                "is_menu" => 0,
                "is_admin" => 1,
                "type" => 3,
                "parent_id" => 0,
                "permission_section" => 0,
                "school_id" => 1,
            ],
            [
                "module" => "",
                "name" => "Add",
                "parent_route" => "dormitory-list-index",
                "lang_name" =>    "common.add",
                "route" => "add-dormitory",
                "status" => 1,
                "menu_status" => 1,
                "position" => 0,
                "is_saas" => 0,
                "relate_to_child" => 0,
                "is_menu" => 0,
                "is_admin" => 1,
                "type" => 3,
                "parent_id" => 0,
                "permission_section" => 0,
                "school_id" => 1,
            ],
            [
                "module" => "",
                "name" => "Edit",
                "parent_route" => "dormitory-list-index",
                "lang_name" =>    "common.edit",
                "route" => "edit-dormitory",
                "status" => 1,
                "menu_status" => 1,
                "position" => 0,
                "is_saas" => 0,
                "relate_to_child" => 0,
                "is_menu" => 0,
                "is_admin" => 1,
                "type" => 3,
                "parent_id" => 0,
                "permission_section" => 0,
                "school_id" => 1,
            ],
            [
                "module" => "",
                "name" => "Delete",
                "parent_route" => "dormitory-list-index",
                "lang_name" =>    "common.delete",
                "route" => "delete-dormitory",
                "status" => 1,
                "menu_status" => 1,
                "position" => 0,
                "is_saas" => 0,
                "relate_to_child" => 0,
                "is_menu" => 0,
                "is_admin" => 1,
                "type" => 3,
                "parent_id" => 0,
                "permission_section" => 0,
                "school_id" => 1,
            ],
            [
                "module" => "",
                "name" => "Add",
                "parent_route" => "room-list-index",
                "lang_name" =>    "common.add",
                "route" => "add-room",
                "status" => 1,
                "menu_status" => 1,
                "position" => 0,
                "is_saas" => 0,
                "relate_to_child" => 0,
                "is_menu" => 0,
                "is_admin" => 1,
                "type" => 3,
                "parent_id" => 0,
                "permission_section" => 0,
                "school_id" => 1,
            ],
            [
                "module" => "",
                "name" => "Edit",
                "parent_route" => "room-list-index",
                "lang_name" =>    "common.edit",
                "route" => "edit-room",
                "status" => 1,
                "menu_status" => 1,
                "position" => 0,
                "is_saas" => 0,
                "relate_to_child" => 0,
                "is_menu" => 0,
                "is_admin" => 1,
                "type" => 3,
                "parent_id" => 0,
                "permission_section" => 0,
                "school_id" => 1,
            ],
            [
                "module" => "",
                "name" => "Delete",
                "parent_route" => "room-list-index",
                "lang_name" =>    "common.delete",
                "route" => "delete-room",
                "status" => 1,
                "menu_status" => 1,
                "position" => 0,
                "is_saas" => 0,
                "relate_to_child" => 0,
                "is_menu" => 0,
                "is_admin" => 1,
                "type" => 3,
                "parent_id" => 0,
                "permission_section" => 0,
                "school_id" => 1,
            ],
            [
                "module" => "",
                "name" => "Add",
                "parent_route" => "room-type-index",
                "lang_name" =>    "common.add",
                "route" => "add-room-type",
                "status" => 1,
                "menu_status" => 1,
                "position" => 0,
                "is_saas" => 0,
                "relate_to_child" => 0,
                "is_menu" => 0,
                "is_admin" => 1,
                "type" => 3,
                "parent_id" => 0,
                "permission_section" => 0,
                "school_id" => 1,
            ],
            [
                "module" => "",
                "name" => "Edit",
                "parent_route" => "room-type-index",
                "lang_name" =>    "common.edit",
                "route" => "edit-room-type",
                "status" => 1,
                "menu_status" => 1,
                "position" => 0,
                "is_saas" => 0,
                "relate_to_child" => 0,
                "is_menu" => 0,
                "is_admin" => 1,
                "type" => 3,
                "parent_id" => 0,
                "permission_section" => 0,
                "school_id" => 1,
            ],
            [
                "module" => "",
                "name" => "Delete",
                "parent_route" => "room-type-index",
                "lang_name" =>    "common.delete",
                "route" => "delete-room-type",
                "status" => 1,
                "menu_status" => 1,
                "position" => 0,
                "is_saas" => 0,
                "relate_to_child" => 0,
                "is_menu" => 0,
                "is_admin" => 1,
                "type" => 3,
                "parent_id" => 0,
                "permission_section" => 0,
                "school_id" => 1,
            ],
            [
                "module" => "",
                "name" => "Restore",
                "parent_route" => "student.delete-student-record",
                "lang_name" =>    "common.restore",
                "route" => "restore-student-record",
                "status" => 1,
                "menu_status" => 1,
                "position" => 0,
                "is_saas" => 0,
                "relate_to_child" => 0,
                "is_menu" => 0,
                "is_admin" => 1,
                "type" => 3,
                "parent_id" => 0,
                "permission_section" => 0,
                "school_id" => 1,
            ],
           
        ];

        foreach($permissions as $permission)
        {
            DB::table('permissions')->where('route',$permission['route'])->delete();
            DB::table('permissions')->insert($permission);
        }
        
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        $permission = ['add-assign-vehicle','edit-assign-vehicle','delete-assign-vehicle','add-dormitory','edit-dormitory','delete-dormitory','add-room','delete-room','add-room-type','edit-room-type','delete-room-type','student_view','student-id-card-preview','student-certificate-view','download-center.content-list-update','restore-student-record'];
        DB::table('permissions')->whereIn('route',$permission)->delete();
    }
};
