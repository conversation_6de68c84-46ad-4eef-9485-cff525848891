# InfixEdu Database Schema - All Tables and Migrations

This document provides a comprehensive overview of all database tables created and modified through Laravel migrations in the InfixEdu v9.0.3 system.

## Overview

- **Total Migration Files**: 300+ files
- **Database System**: Laravel-based School Management System
- **Version**: 9.0.3
- **Migration Period**: 2014-2025

## Core System Tables

### 1. Schools and Authentication

- **sm_schools** (2014_11_01_000001) - Main school information
- **users** (2014_12_01_000003) - System users
- **roles** (2014_12_01_000002) - User roles and permissions
- **password_resets** (2014_11_12_100000) - Password reset tokens

### 2. Academic Structure

- **sm_academic_years** (2014_11_01_000021) - Academic year definitions
- **sm_sessions** (2014_12_01_000022) - Academic sessions
- **sm_classes** (2014_12_01_000006) - Class definitions
- **sm_sections** (2014_12_01_000007) - Section definitions
- **sm_class_sections** (2014_12_01_000008) - Class-section relationships
- **sm_subjects** (2014_12_01_000009) - Subject definitions
- **sm_assign_subjects** (2014_12_01_000065) - Subject assignments

### 3. Student Management

- **sm_students** (2014_12_01_000054) - Student records
- **sm_parents** (2014_12_01_000052) - Parent information
- **student_records** (2018_01_04_105604) - Student academic records
- **sm_student_categories** (2014_12_01_000051) - Student categories
- **sm_student_groups** (2014_12_01_000020) - Student groupings
- **sm_student_attendances** (2014_12_01_000055) - Student attendance
- **sm_student_promotions** (2014_12_01_000056) - Student promotions

### 4. Staff Management

- **sm_staffs** (2014_12_01_000028) - Staff records
- **sm_designations** (2014_12_01_000026) - Job designations
- **sm_human_departments** (2014_12_01_000027) - HR departments
- **sm_staff_attendences** (2014_12_01_000057) - Staff attendance

### 5. Fees Management

- **sm_fees_groups** (2014_12_01_000011) - Fee group definitions
- **sm_fees_types** (2014_12_01_000012) - Fee type definitions
- **sm_fees_discounts** (2014_12_01_000013) - Fee discounts
- **sm_fees_masters** (2014_12_01_000045) - Fee master records
- **sm_fees_assigns** (2018_12_17_104146) - Fee assignments
- **sm_fees_payments** (2018_12_20_090159) - Fee payments
- **sm_fees_carry_forwards** (2018_12_13_093741) - Fee carry forwards

### 6. Examination System

- **sm_exam_types** (2014_12_01_000036) - Exam type definitions
- **sm_exams** (2014_12_01_000038) - Exam records
- **sm_marks_grades** (2014_12_01_000037) - Grading system
- **sm_exam_schedules** (2018_12_31_112538) - Exam schedules
- **sm_exam_schedule_subjects** (2018_12_31_112600) - Exam subject schedules
- **sm_marks_registers** (2019_01_02_061148) - Mark registers
- **sm_marks_register_children** (2019_01_02_061238) - Mark register details

### 7. Online Examination

- **sm_online_exams** (2014_12_01_000035) - Online exam definitions
- **sm_question_banks** (2014_12_01_000034) - Question bank
- **sm_question_groups** (2014_12_01_000033) - Question groupings
- **sm_question_levels** (2014_12_01_000032) - Question difficulty levels
- **sm_online_exam_questions** (2019_01_09_101421) - Online exam questions
- **sm_online_exam_question_mu_options** (2019_01_09_101533) - Multiple choice options

### 8. Library Management

- **sm_book_categories** (2018_12_04_062714) - Book categories
- **sm_books** (2018_12_04_063012) - Book records
- **sm_library_members** (2018_12_04_075138) - Library members
- **sm_book_issues** (2018_12_04_075911) - Book issue records

### 9. Inventory Management

- **sm_item_categories** (2019_01_10_050231) - Item categories
- **sm_items** (2019_01_10_050645) - Inventory items
- **sm_item_stores** (2019_01_10_054622) - Store locations
- **sm_suppliers** (2019_01_10_070859) - Supplier information
- **sm_item_receives** (2019_01_10_112518) - Item receipts
- **sm_item_receive_children** (2019_01_12_104449) - Item receipt details

### 10. Transport Management

- **sm_vehicles** (2014_12_01_000029) - Vehicle records
- **sm_routes** (2014_12_01_000030) - Transport routes
- **sm_assign_vehicles** (2019_01_14_061003) - Vehicle assignments

### 11. Dormitory Management

- **sm_dormitory_lists** (2014_12_01_000023) - Dormitory listings
- **sm_room_types** (2014_12_01_000024) - Room type definitions
- **sm_room_lists** (2014_12_01_000025) - Room listings

### 12. Communication System

- **sm_notice_boards** (2018_12_04_050352) - Notice board
- **sm_send_messages** (2018_12_04_051648) - Message system
- **sm_events** (2018_12_04_060828) - Event management
- **sm_holidays** (2018_12_04_062330) - Holiday calendar

### 13. Financial Management

- **sm_income_heads** (2014_12_01_000014) - Income categories
- **sm_chart_of_accounts** (2014_12_01_000015) - Chart of accounts
- **sm_bank_accounts** (2014_12_01_000016) - Bank account details
- **sm_add_incomes** (2014_12_01_000019) - Income records
- **sm_expense_heads** (2014_12_01_000043) - Expense categories
- **sm_add_expenses** (2014_12_01_000044) - Expense records

### 14. HR and Payroll

- **sm_hr_salary_templates** (2014_12_01_000060) - Salary templates
- **sm_hr_payroll_generates** (2014_12_01_000061) - Payroll generation
- **sm_hr_payroll_earn_deducs** (2018_12_17_111529) - Earnings/deductions
- **sm_leave_types** (2014_12_01_000040) - Leave type definitions
- **sm_leave_defines** (2014_12_01_000041) - Leave definitions
- **sm_leave_requests** (2014_12_01_000042) - Leave requests

### 15. System Configuration

- **sm_base_groups** (2014_12_01_000004) - Base group settings
- **sm_base_setups** (2014_12_01_000005) - Base system setup
- **sm_general_settings** (2019_02_10_125119) - General system settings
- **sm_modules** (2014_12_01_000066) - System modules
- **sm_languages** (2014_12_01_000067) - Language settings
- **sm_date_formats** (2014_12_01_000068) - Date format settings

## OAuth and API Tables

- **oauth_auth_codes** (2016_06_01_000001) - OAuth authorization codes
- **oauth_access_tokens** (2016_06_01_000002) - OAuth access tokens
- **oauth_refresh_tokens** (2016_06_01_000003) - OAuth refresh tokens
- **oauth_clients** (2016_06_01_000004) - OAuth client applications
- **oauth_personal_access_clients** (2016_06_01_000005) - Personal access clients

## Recent Additions (2023-2025)

### Version 8.x Updates

- **all_exam_wise_positions** (2023_03_10_123138) - Exam position tracking
- **sm_exam_signatures** (2023_05_19_041042) - Exam signatures
- **maintenance_settings** (2023_05_24_081000) - System maintenance
- **sm_notification_settings** (2023_06_05_061123) - Notification preferences
- **pulse_tables** (2023_06_07_000001) - Performance monitoring
- **teacher_evaluations** (2023_07_11_094614) - Teacher evaluation system
- **calendar_menus** (2023_07_19_061034) - Calendar menu management

### Frontend and CMS

- **home_sliders** (2023_09_26_094106) - Homepage sliders
- **sm_expert_teachers** (2023_09_27_065756) - Expert teacher profiles
- **sm_photo_galleries** (2023_09_28_054606) - Photo gallery
- **sm_video_galleries** (2023_09_29_052332) - Video gallery
- **front_results** (2023_09_30_040648) - Frontend result display
- **front_exam_routines** (2023_10_03_054024) - Frontend exam routines
- **front_class_routines** (2023_10_03_054032) - Frontend class routines

### Version 9.x Features

- **shifts** (2025_05_19_065023) - Shift management system
- **plugins** (2023_12_07_121858) - Plugin management
- **sm_donors** (2023_12_11_063723) - Donor management
- **sm_form_downloads** (2023_12_13_024824) - Form download tracking

## Table Modifications and Updates

The system includes numerous migration files that modify existing tables:

### Major Update Migrations

- **2021_11_16_062629_base_setup_update.php** - Base setup synchronization across schools
- **2023_05_09_053931_add_version_7_0_0_migration.php** - Version 7.0.0 comprehensive updates
- **2023_05_19_053931_add_version_7_0_2_migration.php** - Version 7.0.2 updates
- **2023_06_02_053931_add_version_7_0_3_migration.php** - Version 7.0.3 updates
- **2023_08_25_062823_create_update_7.1.1_to_7.2.0_table.php** - Version 7.2.0 updates
- **2023_09_11_0628897_create_update_7.2.0_to_7.2.1_table.php** - Version 7.2.1 updates
- **2023_09_22_1346345_update_v721_to_v8_migrations.php** - Version 8.0 migration
- **2023_12_04_122708_update_v8.0.1_to_8.1.0.php** - Version 8.1.0 updates
- **2024_01_30_051708_update_v8.1.0_to_8.1.1.php** - Version 8.1.1 updates
- **2024_03_05_081452_update_v8.1.1_to_8.1.2.php** - Version 8.1.2 updates
- **2024_03_15_081452_update_v8.1.2_to_8.2.0.php** - Version 8.2.0 updates
- **2024_07_15_081453_update_v8.2.2.php** - Version 8.2.2 updates
- **2024_09_13_081453_update_v8.2.4.php** - Version 8.2.4 updates
- **2024_09_19_081453_update_v8.2.5.php** - Version 8.2.5 updates
- **2024_12_27_033444_update_8.2.7.php** - Version 8.2.7 updates
- **2024_12_27_033445_update_8.2.8.php** - Version 8.2.8 updates
- **2025_01_23_075305_version_8.2.8_update_migration.php** - Version 8.2.8 final updates

### Specific Column Additions and Modifications

#### Student Management Enhancements

- **sm_student_id_cards** table optimizations (2025_02_18_033910):
  - Column length optimizations for better performance
  - Title, layout style, photo style field adjustments
- **sm_student_certificates** table updates (2023_11_22_034222, 2025_02_18_034756):
  - Added body_two, certificate_no, type columns
  - Performance optimizations
- **sm_students** table modifications (2025_02_27_043531):
  - Student category and classification updates

#### System Configuration Updates

- **sm_general_settings** table enhancements:
  - role_based_sidebar column (2024_09_05_184427)
  - carry_forword_due_day column (2025_08_11_101934)
  - shift_enable column (2025_06_02_060230)
- **permissions** table updates (2024_09_05_191641):
  - Added role_id column for role-based permissions

#### Academic System Improvements

- **sm_classes** and **sm_sections** optimizations (2025_02_18_093303)
- **shifts** table integration (2025_05_19_065023, 2025_06_11_145721):
  - New shift management system
  - Academic year integration
- **all_exam_wise_positions** grade column type changes (2025_05_15_093132)

#### Fees Management Updates

- **fm_fees_groups** and **fm_fees_types** modifications (2025_03_04_084527, 2025_03_04_084919)
- **sm_fees_carry_forwards** enhancements:
  - balance_type and due_date columns
  - Carry forward settings integration

#### Multi-School and Multi-Tenancy

- Consistent school_id foreign key additions across multiple tables
- Academic year relationships strengthened
- Permission system enhancements for multi-school support

### Data Migration and Seeding Updates

Many migrations include data updates and seeding:

- SMS template updates and standardization
- Menu and permission synchronization
- Default data insertion for new features
- Module activation and configuration

## Database Relationships

The system maintains complex relationships between tables:

- **Foreign Key Constraints**: Extensive use of foreign keys for data integrity
- **School-based Multi-tenancy**: Most tables include school_id for multi-school support
- **Academic Year Relationships**: Many tables link to academic years and sessions
- **User Role Integration**: Comprehensive role-based access control

## Migration Patterns

1. **Table Creation**: Standard Laravel migration patterns
2. **Column Additions**: Conditional column additions with Schema::hasColumn checks
3. **Data Seeding**: Many migrations include default data insertion
4. **Version Updates**: Dedicated migrations for version upgrades
5. **Multi-school Support**: Consistent school_id foreign key implementation

---

_This document represents the database schema as of InfixEdu v9.0.3. The system continues to evolve with regular updates and new features._
