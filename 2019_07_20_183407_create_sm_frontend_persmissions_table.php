<?php

use App\SmFrontendPersmission;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSmFrontendPersmissionsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sm_frontend_persmissions', function (Blueprint $blueprint): void {
            $blueprint->increments('id');
            $blueprint->string('name', 255)->nullable();
            $blueprint->integer('parent_id')->default(0);
            $blueprint->integer('is_published')->default(0);
            $blueprint->integer('school_id')->nullable()->default(1)->unsigned();
            $blueprint->foreign('school_id')->references('id')->on('sm_schools')->onDelete('cascade');
            $blueprint->timestamps();
        });

        $s = new SmFrontendPersmission();
        $s->name = 'Home Page';
        $s->parent_id = 0;
        $s->is_published = 1;
        $s->save(); // ID=1

        $s = new SmFrontendPersmission();
        $s->name = 'About Page';
        $s->parent_id = 1;
        $s->is_published = 1;
        $s->save(); // ID=2

        $s = new SmFrontendPersmission();
        $s->name = 'Image Banner';
        $s->parent_id = 1;
        $s->is_published = 1;
        $s->save();

        $s = new SmFrontendPersmission();
        $s->name = 'Latest News';
        $s->parent_id = 1;
        $s->is_published = 1;
        $s->save();

        $s = new SmFrontendPersmission();
        $s->name = 'Notice Board';
        $s->parent_id = 1;
        $s->is_published = 1;
        $s->save();

        $s = new SmFrontendPersmission();
        $s->name = 'Event List';
        $s->parent_id = 1;
        $s->is_published = 1;
        $s->save();
        $s = new SmFrontendPersmission();
        $s->name = 'Academics';
        $s->parent_id = 1;
        $s->is_published = 1;
        $s->save();

        $s = new SmFrontendPersmission();
        $s->name = 'Testimonial';
        $s->parent_id = 1;
        $s->is_published = 1;
        $s->save();

        $s = new SmFrontendPersmission();
        $s->name = 'Custom Links';
        $s->parent_id = 1;
        $s->is_published = 1;
        $s->save();

        $s = new SmFrontendPersmission();
        $s->name = 'Social Icons';
        $s->parent_id = 1;
        $s->is_published = 1;
        $s->save();

        $s = new SmFrontendPersmission();
        $s->name = 'About Image';
        $s->parent_id = 2;
        $s->is_published = 1;
        $s->save();

        $s = new SmFrontendPersmission();
        $s->name = 'Statistic Number Section';
        $s->parent_id = 2;
        $s->is_published = 1;
        $s->save();

        $s = new SmFrontendPersmission();
        $s->name = 'Our History';
        $s->parent_id = 2;
        $s->is_published = 1;
        $s->save();

        $s = new SmFrontendPersmission();
        $s->name = 'Our Mission and Vision';
        $s->parent_id = 2;
        $s->is_published = 1;
        $s->save();

        $s = new SmFrontendPersmission();
        $s->name = 'Testimonial';
        $s->parent_id = 2;
        $s->is_published = 1;
        $s->save();

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sm_frontend_persmissions');
    }
}
