<?php

use App\SmTimeZone;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSmTimeZonesTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sm_time_zones', function (Blueprint $blueprint): void {
            $blueprint->increments('id');
            $blueprint->string('code')->nullable();
            $blueprint->string('time_zone')->nullable();
            $blueprint->timestamps();
        });
        $data = [
            ['AD', 'Europe/Andorra'],
            ['AE', 'Asia/Dubai'],
            ['AF', 'Asia/Kabul'],
            ['AG', 'America/Antigua'],
            ['AI', 'America/Anguilla'],
            ['AL', 'Europe/Tirane'],
            ['AM', 'Asia/Yerevan'],
            ['AO', 'Africa/Luanda'],
            ['AQ', 'Antarctica/McMurdo'],
            ['AQ', 'Antarctica/Casey'],
            ['AQ', 'Antarctica/Davis'],
            ['AQ', 'Antarctica/DumontDUrville'],
            ['AQ', 'Antarctica/Mawson'],
            ['AQ', 'Antarctica/Palmer'],
            ['AQ', 'Antarctica/Rothera'],
            ['AQ', 'Antarctica/Syowa'],
            ['AQ', 'Antarctica/Troll'],
            ['AQ', 'Antarctica/Vostok'],
            ['AR', 'America/Argentina/Buenos_Aires'],
            ['AR', 'America/Argentina/Cordoba'],
            ['AR', 'America/Argentina/Salta'],
            ['AR', 'America/Argentina/Jujuy'],
            ['AR', 'America/Argentina/Tucuman'],
            ['AR', 'America/Argentina/Catamarca'],
            ['AR', 'America/Argentina/La_Rioja'],
            ['AR', 'America/Argentina/San_Juan'],
            ['AR', 'America/Argentina/Mendoza'],
            ['AR', 'America/Argentina/San_Luis'],
            ['AR', 'America/Argentina/Rio_Gallegos'],
            ['AR', 'America/Argentina/Ushuaia'],
            ['AS', 'Pacific/Pago_Pago'],
            ['AT', 'Europe/Vienna'],
            ['AU', 'Australia/Lord_Howe'],
            ['AU', 'Antarctica/Macquarie'],
            ['AU', 'Australia/Hobart'],
            ['AU', 'Australia/Currie'],
            ['AU', 'Australia/Melbourne'],
            ['AU', 'Australia/Sydney'],
            ['AU', 'Australia/Broken_Hill'],
            ['AU', 'Australia/Brisbane'],
            ['AU', 'Australia/Lindeman'],
            ['AU', 'Australia/Adelaide'],
            ['AU', 'Australia/Darwin'],
            ['AU', 'Australia/Perth'],
            ['AU', 'Australia/Eucla'],
            ['AW', 'America/Aruba'],
            ['AX', 'Europe/Mariehamn'],
            ['AZ', 'Asia/Baku'],
            ['BA', 'Europe/Sarajevo'],
            ['BB', 'America/Barbados'],
            ['BD', 'Asia/Dhaka'],
            ['BE', 'Europe/Brussels'],
            ['BF', 'Africa/Ouagadougou'],
            ['BG', 'Europe/Sofia'],
            ['BH', 'Asia/Bahrain'],
            ['BI', 'Africa/Bujumbura'],
            ['BJ', 'Africa/Porto-Novo'],
            ['BL', 'America/St_Barthelemy'],
            ['BM', 'Atlantic/Bermuda'],
            ['BN', 'Asia/Brunei'],
            ['BO', 'America/La_Paz'],
            ['BQ', 'America/Kralendijk'],
            ['BR', 'America/Noronha'],
            ['BR', 'America/Belem'],
            ['BR', 'America/Fortaleza'],
            ['BR', 'America/Recife'],
            ['BR', 'America/Araguaina'],
            ['BR', 'America/Maceio'],
            ['BR', 'America/Bahia'],
            ['BR', 'America/Sao_Paulo'],
            ['BR', 'America/Campo_Grande'],
            ['BR', 'America/Cuiaba'],
            ['BR', 'America/Santarem'],
            ['BR', 'America/Porto_Velho'],
            ['BR', 'America/Boa_Vista'],
            ['BR', 'America/Manaus'],
            ['BR', 'America/Eirunepe'],
            ['BR', 'America/Rio_Branco'],
            ['BS', 'America/Nassau'],
            ['BT', 'Asia/Thimphu'],
            ['BW', 'Africa/Gaborone'],
            ['BY', 'Europe/Minsk'],
            ['BZ', 'America/Belize'],
            ['CA', 'America/St_Johns'],
            ['CA', 'America/Halifax'],
            ['CA', 'America/Glace_Bay'],
            ['CA', 'America/Moncton'],
            ['CA', 'America/Goose_Bay'],
            ['CA', 'America/Blanc-Sablon'],
            ['CA', 'America/Toronto'],
            ['CA', 'America/Nipigon'],
            ['CA', 'America/Thunder_Bay'],
            ['CA', 'America/Iqaluit'],
            ['CA', 'America/Pangnirtung'],
            ['CA', 'America/Atikokan'],
            ['CA', 'America/Winnipeg'],
            ['CA', 'America/Rainy_River'],
            ['CA', 'America/Resolute'],
            ['CA', 'America/Rankin_Inlet'],
            ['CA', 'America/Regina'],
            ['CA', 'America/Swift_Current'],
            ['CA', 'America/Edmonton'],
            ['CA', 'America/Cambridge_Bay'],
            ['CA', 'America/Yellowknife'],
            ['CA', 'America/Inuvik'],
            ['CA', 'America/Creston'],
            ['CA', 'America/Dawson_Creek'],
            ['CA', 'America/Fort_Nelson'],
            ['CA', 'America/Vancouver'],
            ['CA', 'America/Whitehorse'],
            ['CA', 'America/Dawson'],
            ['CC', 'Indian/Cocos'],
            ['CD', 'Africa/Kinshasa'],
            ['CD', 'Africa/Lubumbashi'],
            ['CF', 'Africa/Bangui'],
            ['CG', 'Africa/Brazzaville'],
            ['CH', 'Europe/Zurich'],
            ['CI', 'Africa/Abidjan'],
            ['CK', 'Pacific/Rarotonga'],
            ['CL', 'America/Santiago'],
            ['CL', 'America/Punta_Arenas'],
            ['CL', 'Pacific/Easter'],
            ['CM', 'Africa/Douala'],
            ['CN', 'Asia/Shanghai'],
            ['CN', 'Asia/Urumqi'],
            ['CO', 'America/Bogota'],
            ['CR', 'America/Costa_Rica'],
            ['CU', 'America/Havana'],
            ['CV', 'Atlantic/Cape_Verde'],
            ['CW', 'America/Curacao'],
            ['CX', 'Indian/Christmas'],
            ['CY', 'Asia/Nicosia'],
            ['CY', 'Asia/Famagusta'],
            ['CZ', 'Europe/Prague'],
            ['DE', 'Europe/Berlin'],
            ['DE', 'Europe/Busingen'],
            ['DJ', 'Africa/Djibouti'],
            ['DK', 'Europe/Copenhagen'],
            ['DM', 'America/Dominica'],
            ['DO', 'America/Santo_Domingo'],
            ['DZ', 'Africa/Algiers'],
            ['EC', 'America/Guayaquil'],
            ['EC', 'Pacific/Galapagos'],
            ['EE', 'Europe/Tallinn'],
            ['EG', 'Africa/Cairo'],
            ['EH', 'Africa/El_Aaiun'],
            ['ER', 'Africa/Asmara'],
            ['ES', 'Europe/Madrid'],
            ['ES', 'Africa/Ceuta'],
            ['ES', 'Atlantic/Canary'],
            ['ET', 'Africa/Addis_Ababa'],
            ['FI', 'Europe/Helsinki'],
            ['FJ', 'Pacific/Fiji'],
            ['FK', 'Atlantic/Stanley'],
            ['FM', 'Pacific/Chuuk'],
            ['FM', 'Pacific/Pohnpei'],
            ['FM', 'Pacific/Kosrae'],
            ['FO', 'Atlantic/Faroe'],
            ['FR', 'Europe/Paris'],
            ['GA', 'Africa/Libreville'],
            ['GB', 'Europe/London'],
            ['GD', 'America/Grenada'],
            ['GE', 'Asia/Tbilisi'],
            ['GF', 'America/Cayenne'],
            ['GG', 'Europe/Guernsey'],
            ['GH', 'Africa/Accra'],
            ['GI', 'Europe/Gibraltar'],
            ['GL', 'America/Godthab'],
            ['GL', 'America/Danmarkshavn'],
            ['GL', 'America/Scoresbysund'],
            ['GL', 'America/Thule'],
            ['GM', 'Africa/Banjul'],
            ['GN', 'Africa/Conakry'],
            ['GP', 'America/Guadeloupe'],
            ['GQ', 'Africa/Malabo'],
            ['GR', 'Europe/Athens'],
            ['GS', 'Atlantic/South_Georgia'],
            ['GT', 'America/Guatemala'],
            ['GU', 'Pacific/Guam'],
            ['GW', 'Africa/Bissau'],
            ['GY', 'America/Guyana'],
            ['HK', 'Asia/Hong_Kong'],
            ['HN', 'America/Tegucigalpa'],
            ['HR', 'Europe/Zagreb'],
            ['HT', 'America/Port-au-Prince'],
            ['HU', 'Europe/Budapest'],
            ['ID', 'Asia/Jakarta'],
            ['ID', 'Asia/Pontianak'],
            ['ID', 'Asia/Makassar'],
            ['ID', 'Asia/Jayapura'],
            ['IE', 'Europe/Dublin'],
            ['IL', 'Asia/Jerusalem'],
            ['IM', 'Europe/Isle_of_Man'],
            ['IN', 'Asia/Kolkata'],
            ['IO', 'Indian/Chagos'],
            ['IQ', 'Asia/Baghdad'],
            ['IR', 'Asia/Tehran'],
            ['IS', 'Atlantic/Reykjavik'],
            ['IT', 'Europe/Rome'],
            ['JE', 'Europe/Jersey'],
            ['JM', 'America/Jamaica'],
            ['JO', 'Asia/Amman'],
            ['JP', 'Asia/Tokyo'],
            ['KE', 'Africa/Nairobi'],
            ['KG', 'Asia/Bishkek'],
            ['KH', 'Asia/Phnom_Penh'],
            ['KI', 'Pacific/Tarawa'],
            ['KI', 'Pacific/Enderbury'],
            ['KI', 'Pacific/Kiritimati'],
            ['KM', 'Indian/Comoro'],
            ['KN', 'America/St_Kitts'],
            ['KP', 'Asia/Pyongyang'],
            ['KR', 'Asia/Seoul'],
            ['KW', 'Asia/Kuwait'],
            ['KY', 'America/Cayman'],
            ['KZ', 'Asia/Almaty'],
            ['KZ', 'Asia/Qyzylorda'],
            ['KZ', 'Asia/Aqtobe'],
            ['KZ', 'Asia/Aqtau'],
            ['KZ', 'Asia/Atyrau'],
            ['KZ', 'Asia/Oral'],
            ['LA', 'Asia/Vientiane'],
            ['LB', 'Asia/Beirut'],
            ['LC', 'America/St_Lucia'],
            ['LI', 'Europe/Vaduz'],
            ['LK', 'Asia/Colombo'],
            ['LR', 'Africa/Monrovia'],
            ['LS', 'Africa/Maseru'],
            ['LT', 'Europe/Vilnius'],
            ['LU', 'Europe/Luxembourg'],
            ['LV', 'Europe/Riga'],
            ['LY', 'Africa/Tripoli'],
            ['MA', 'Africa/Casablanca'],
            ['MC', 'Europe/Monaco'],
            ['MD', 'Europe/Chisinau'],
            ['ME', 'Europe/Podgorica'],
            ['MF', 'America/Marigot'],
            ['MG', 'Indian/Antananarivo'],
            ['MH', 'Pacific/Majuro'],
            ['MH', 'Pacific/Kwajalein'],
            ['MK', 'Europe/Skopje'],
            ['ML', 'Africa/Bamako'],
            ['MM', 'Asia/Yangon'],
            ['MN', 'Asia/Ulaanbaatar'],
            ['MN', 'Asia/Hovd'],
            ['MN', 'Asia/Choibalsan'],
            ['MO', 'Asia/Macau'],
            ['MP', 'Pacific/Saipan'],
            ['MQ', 'America/Martinique'],
            ['MR', 'Africa/Nouakchott'],
            ['MS', 'America/Montserrat'],
            ['MT', 'Europe/Malta'],
            ['MU', 'Indian/Mauritius'],
            ['MV', 'Indian/Maldives'],
            ['MW', 'Africa/Blantyre'],
            ['MX', 'America/Mexico_City'],
            ['MX', 'America/Cancun'],
            ['MX', 'America/Merida'],
            ['MX', 'America/Monterrey'],
            ['MX', 'America/Matamoros'],
            ['MX', 'America/Mazatlan'],
            ['MX', 'America/Chihuahua'],
            ['MX', 'America/Ojinaga'],
            ['MX', 'America/Hermosillo'],
            ['MX', 'America/Tijuana'],
            ['MX', 'America/Bahia_Banderas'],
            ['MY', 'Asia/Kuala_Lumpur'],
            ['MY', 'Asia/Kuching'],
            ['MZ', 'Africa/Maputo'],
            ['NA', 'Africa/Windhoek'],
            ['NC', 'Pacific/Noumea'],
            ['NE', 'Africa/Niamey'],
            ['NF', 'Pacific/Norfolk'],
            ['NG', 'Africa/Lagos'],
            ['NI', 'America/Managua'],
            ['NL', 'Europe/Amsterdam'],
            ['NO', 'Europe/Oslo'],
            ['NP', 'Asia/Kathmandu'],
            ['NR', 'Pacific/Nauru'],
            ['NU', 'Pacific/Niue'],
            ['NZ', 'Pacific/Auckland'],
            ['NZ', 'Pacific/Chatham'],
            ['OM', 'Asia/Muscat'],
            ['PA', 'America/Panama'],
            ['PE', 'America/Lima'],
            ['PF', 'Pacific/Tahiti'],
            ['PF', 'Pacific/Marquesas'],
            ['PF', 'Pacific/Gambier'],
            ['PG', 'Pacific/Port_Moresby'],
            ['PG', 'Pacific/Bougainville'],
            ['PH', 'Asia/Manila'],
            ['PK', 'Asia/Karachi'],
            ['PL', 'Europe/Warsaw'],
            ['PM', 'America/Miquelon'],
            ['PN', 'Pacific/Pitcairn'],
            ['PR', 'America/Puerto_Rico'],
            ['PS', 'Asia/Gaza'],
            ['PS', 'Asia/Hebron'],
            ['PT', 'Europe/Lisbon'],
            ['PT', 'Atlantic/Madeira'],
            ['PT', 'Atlantic/Azores'],
            ['PW', 'Pacific/Palau'],
            ['PY', 'America/Asuncion'],
            ['QA', 'Asia/Qatar'],
            ['RE', 'Indian/Reunion'],
            ['RO', 'Europe/Bucharest'],
            ['RS', 'Europe/Belgrade'],
            ['RU', 'Europe/Kaliningrad'],
            ['RU', 'Europe/Moscow'],
            ['RU', 'Europe/Simferopol'],
            ['RU', 'Europe/Volgograd'],
            ['RU', 'Europe/Kirov'],
            ['RU', 'Europe/Astrakhan'],
            ['RU', 'Europe/Saratov'],
            ['RU', 'Europe/Ulyanovsk'],
            ['RU', 'Europe/Samara'],
            ['RU', 'Asia/Yekaterinburg'],
            ['RU', 'Asia/Omsk'],
            ['RU', 'Asia/Novosibirsk'],
            ['RU', 'Asia/Barnaul'],
            ['RU', 'Asia/Tomsk'],
            ['RU', 'Asia/Novokuznetsk'],
            ['RU', 'Asia/Krasnoyarsk'],
            ['RU', 'Asia/Irkutsk'],
            ['RU', 'Asia/Chita'],
            ['RU', 'Asia/Yakutsk'],
            ['RU', 'Asia/Khandyga'],
            ['RU', 'Asia/Vladivostok'],
            ['RU', 'Asia/Ust-Nera'],
            ['RU', 'Asia/Magadan'],
            ['RU', 'Asia/Sakhalin'],
            ['RU', 'Asia/Srednekolymsk'],
            ['RU', 'Asia/Kamchatka'],
            ['RU', 'Asia/Anadyr'],
            ['RW', 'Africa/Kigali'],
            ['SA', 'Asia/Riyadh'],
            ['SB', 'Pacific/Guadalcanal'],
            ['SC', 'Indian/Mahe'],
            ['SD', 'Africa/Khartoum'],
            ['SE', 'Europe/Stockholm'],
            ['SG', 'Asia/Singapore'],
            ['SH', 'Atlantic/St_Helena'],
            ['SI', 'Europe/Ljubljana'],
            ['SJ', 'Arctic/Longyearbyen'],
            ['SK', 'Europe/Bratislava'],
            ['SL', 'Africa/Freetown'],
            ['SM', 'Europe/San_Marino'],
            ['SN', 'Africa/Dakar'],
            ['SO', 'Africa/Mogadishu'],
            ['SR', 'America/Paramaribo'],
            ['SS', 'Africa/Juba'],
            ['ST', 'Africa/Sao_Tome'],
            ['SV', 'America/El_Salvador'],
            ['SX', 'America/Lower_Princes'],
            ['SY', 'Asia/Damascus'],
            ['SZ', 'Africa/Mbabane'],
            ['TC', 'America/Grand_Turk'],
            ['TD', 'Africa/Ndjamena'],
            ['TF', 'Indian/Kerguelen'],
            ['TG', 'Africa/Lome'],
            ['TH', 'Asia/Bangkok'],
            ['TJ', 'Asia/Dushanbe'],
            ['TK', 'Pacific/Fakaofo'],
            ['TL', 'Asia/Dili'],
            ['TM', 'Asia/Ashgabat'],
            ['TN', 'Africa/Tunis'],
            ['TO', 'Pacific/Tongatapu'],
            ['TR', 'Europe/Istanbul'],
            ['TT', 'America/Port_of_Spain'],
            ['TV', 'Pacific/Funafuti'],
            ['TW', 'Asia/Taipei'],
            ['TZ', 'Africa/Dar_es_Salaam'],
            ['UA', 'Europe/Kiev'],
            ['UA', 'Europe/Uzhgorod'],
            ['UA', 'Europe/Zaporozhye'],
            ['UG', 'Africa/Kampala'],
            ['UM', 'Pacific/Midway'],
            ['UM', 'Pacific/Wake'],
            ['US', 'America/New_York'],
            ['US', 'America/Detroit'],
            ['US', 'America/Kentucky/Louisville'],
            ['US', 'America/Kentucky/Monticello'],
            ['US', 'America/Indiana/Indianapolis'],
            ['US', 'America/Indiana/Vincennes'],
            ['US', 'America/Indiana/Winamac'],
            ['US', 'America/Indiana/Marengo'],
            ['US', 'America/Indiana/Petersburg'],
            ['US', 'America/Indiana/Vevay'],
            ['US', 'America/Chicago'],
            ['US', 'America/Indiana/Tell_City'],
            ['US', 'America/Indiana/Knox'],
            ['US', 'America/Menominee'],
            ['US', 'America/North_Dakota/Center'],
            ['US', 'America/North_Dakota/New_Salem'],
            ['US', 'America/North_Dakota/Beulah'],
            ['US', 'America/Denver'],
            ['US', 'America/Boise'],
            ['US', 'America/Phoenix'],
            ['US', 'America/Los_Angeles'],
            ['US', 'America/Anchorage'],
            ['US', 'America/Juneau'],
            ['US', 'America/Sitka'],
            ['US', 'America/Metlakatla'],
            ['US', 'America/Yakutat'],
            ['US', 'America/Nome'],
            ['US', 'America/Adak'],
            ['US', 'Pacific/Honolulu'],
            ['UY', 'America/Montevideo'],
            ['UZ', 'Asia/Samarkand'],
            ['UZ', 'Asia/Tashkent'],
            ['VA', 'Europe/Vatican'],
            ['VC', 'America/St_Vincent'],
            ['VE', 'America/Caracas'],
            ['VG', 'America/Tortola'],
            ['VI', 'America/St_Thomas'],
            ['VN', 'Asia/Ho_Chi_Minh'],
            ['VU', 'Pacific/Efate'],
            ['WF', 'Pacific/Wallis'],
            ['WS', 'Pacific/Apia'],
            ['YE', 'Asia/Aden'],
            ['YT', 'Indian/Mayotte'],
            ['ZA', 'Africa/Johannesburg'],
            ['ZM', 'Africa/Lusaka'],
            ['ZW', 'Africa/Harare'],
        ];
        foreach ($data as $row) {
            $s = new SmTimeZone();
            $s->code = $row[0];
            $s->time_zone = $row[1];
            $s->save();
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sm_time_zones');
    }
}
