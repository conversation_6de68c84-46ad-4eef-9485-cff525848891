<?php

use App\SmModuleLink;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSmModuleLinksTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sm_module_links', function (Blueprint $blueprint): void {
            $blueprint->increments('id');
            $blueprint->integer('module_id')->nullable()->unsigned();
            $blueprint->foreign('module_id')->references('id')->on('sm_modules')->onDelete('cascade');

            $blueprint->string('name')->nullable();
            $blueprint->string('route')->nullable();
            $blueprint->tinyInteger('active_status')->default(1);
            $blueprint->integer('created_by')->nullable()->default(1)->unsigned();
            $blueprint->foreign('created_by')->references('id')->on('users')->onDelete('cascade');

            $blueprint->integer('updated_by')->nullable()->default(1)->unsigned();
            $blueprint->foreign('updated_by')->references('id')->on('users')->onDelete('cascade');

            $blueprint->integer('school_id')->nullable()->default(1)->unsigned();
            $blueprint->foreign('school_id')->references('id')->on('sm_schools')->onDelete('cascade');
            $blueprint->timestamps();
        });

        //   $sql = "INSERT INTO `sm_module_links` (`id`, `module_id`, `name`, `route`, `active_status`, `created_by`, `updated_by`, `school_id`, `created_at`, `updated_at`) VALUES

        $module_infos = [
            [1, 1, 'Dashboard Menu', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [2, 1, '➡ Number of Student', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [3, 1, '➡ Number of Teacher', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [4, 1, '➡ Number of Parents', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [5, 1, '➡ Number of Staff', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [6, 1, '➡ Current Month Income and Expense Chart', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [7, 1, '➡ Current Year Income and Expense Chart', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [8, 1, '➡ Notice Board', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [9, 1, '➡ Calendar Section', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [10, 1, '➡ To Do list', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            // -- Admin Section Menu
            [11, 2, 'Admin Section Menu', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [12, 2, 'Admission Query menu', 'admission-query', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [13, 2, '➡ Create Query Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [14, 2, '➡ Create Query Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [15, 2, '➡ Create Query Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [16, 2, 'Visitor Book Menu', 'visitor', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [17, 2, '➡ Visitor  Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [18, 2, '➡ Visitor Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [19, 2, '➡ Visitor Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [20, 2, '➡ Visitor Download', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [21, 2, 'Complaint Menu', 'complaint', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [22, 2, '➡ Complaint Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [23, 2, '➡ Complaint Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [24, 2, '➡ Complaint Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [25, 2, '➡ Complaint Download', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [26, 2, '➡ Complaint View', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [27, 2, 'Postal Receive Menu', 'postal-receive', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [28, 2, '➡ Postal Receive Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [29, 2, '➡ Postal Receive Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [30, 2, '➡ Postal Receive Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [31, 2, '➡ Postal Receive Download', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [32, 2, 'Postal Dispatch Menu', 'postal-dispatch', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [33, 2, '➡ Postal Dispatch Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [34, 2, '➡ Postal Dispatch Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [35, 2, '➡ Postal Dispatch Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [36, 2, 'Phone Call Log Menu', 'phone-call', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [37, 2, '➡ Phone Call Log Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [38, 2, '➡ Phone Call Log Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [39, 2, '➡ Phone Call Log Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [40, 2, '➡ Phone Call Log Download', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [41, 2, 'Admin Setup Menu', 'setup-admin', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [42, 2, '➡ Admin Setup Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [43, 2, '➡ Admin Setup Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [44, 2, '➡ Admin Setup Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [45, 2, 'Student ID Menu', 'student-id-card', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [46, 2, '➡ Student ID Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [47, 2, '➡ Student ID Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [48, 2, '➡ Student ID Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [49, 2, 'Student Certificate Menu', 'student-certificate', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [50, 2, '➡ Student Certificate Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [51, 2, '➡ Student Certificate Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [52, 2, '➡ Student Certificate Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [53, 2, 'Generate Certificate Menu', 'generate-certificate', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [54, 2, '➡ Generate Certificate Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [55, 2, '➡ Generate Certificate Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [56, 2, '➡ Generate Certificate Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [57, 2, 'Generate ID Card Menu', 'generate-id-card', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [58, 2, '➡ Generate ID Card Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [59, 2, '➡ Generate ID Card Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [60, 2, '➡ Generate ID Card Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            // -- end Admin Menu

            // -- Start Student

            [61, 3, 'Student Menu', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [62, 3, 'Student Admission Menu', 'student-admission', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [63, 3, '➡ Import Student', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [64, 3, 'Student List Menu', 'student-list', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [65, 3, '➡ Student List Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [66, 3, '➡ Student List Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [67, 3, '➡ Student List Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [68, 3, 'Student Attendance Menu', 'student-attendance', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [69, 3, '➡ Student Attendance Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [70, 3, 'Student Attendance Report Menu', 'student-attendance-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [71, 3, 'Student Category Menu', 'student-category', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [72, 3, '➡ Student Category Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [73, 3, '➡ Student Category Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [74, 3, '➡ Student Category Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [75, 3, '➡ Student Category Download', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [76, 3, 'Student Group Menu', 'student-group', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [77, 3, '➡ Student Group Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [79, 3, '➡ Student Group Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [80, 3, '➡ Student Group Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [81, 3, 'Student Promote Menu', 'student-promote', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [82, 3, '➡ Student Promote Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [83, 3, 'Disabled Students Menu', 'disabled-student', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [84, 3, '➡ Disabled Students Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [85, 3, '➡ Disabled Students Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [86, 3, '➡ Disabled Students Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            // -- end Student Menu

            // -- Start Teacher Menu

            [87, 4, 'Teacher Section Menu', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [88, 4, 'Upload Content Menu', 'upload-content', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [89, 4, '➡ Create Content Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [90, 4, '➡ Content Download', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [91, 4, '➡ Content Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [92, 4, 'Assignment Menu', 'assignment-list', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [93, 4, '➡ Create Assignment Add', '', 0, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [94, 4, '➡ Assignment Download', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [95, 4, '➡ Assignment Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [96, 4, 'Study Material Menu', 'study-metarial-list', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [97, 4, '➡ Create Study Material Add', '', 0, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [98, 4, '➡ Study Material Download', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [99, 4, '➡ Study Material Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [100, 4, 'Syllabus Menu', 'syllabus-list', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [101, 4, '➡ Create Study Syllabus Add', '', 0, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [102, 4, '➡ Study Syllabus Edit', '', 0, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [103, 4, '➡ Study Syllabus Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [104, 4, '➡ Study Syllabus Download', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [105, 4, 'Other Downloads Menu', 'other-download-list', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [106, 4, '➡ Other Downloads Download', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [107, 4, '➡ Other Downloads Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            // -- end Teacher Menu

            // -- Start Fees Collection Menu

            [108, 5, 'Fees Collection Menu', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [109, 5, 'Collect Fees Menu', 'collect-fees', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [110, 5, '➡ Create Collect Fees', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [111, 5, '➡ Collect Fees Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [112, 5, '➡ Collect Fees Print', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [113, 5, 'Search Fees Payment Menu', 'search-fees-payment', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [114, 5, '➡ Create Search Fees Payment Add', '', 0, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [115, 5, '➡ Search Fees Payment View', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [116, 5, 'Search Fees Due Menu', 'search-fees-due', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [117, 5, '➡ Search Fees Due View', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [118, 5, 'Fees Master Menu', 'fees-master', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [119, 5, '➡ Create Fees Master Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [120, 5, '➡ Fees Master Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [121, 5, '➡ Fees Master Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [122, 5, '➡ Fees Master Assign', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [123, 5, 'Fees Group Menu', 'fees-group', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [124, 5, '➡ Create Fees Group Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [125, 5, '➡ Fees Group Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [126, 5, '➡ Fees Group Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [127, 5, 'Fees Type Menu', 'fees-type', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [128, 5, '➡ Create Fees Type Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [129, 5, '➡ Fees Type Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [130, 5, '➡ Fees Type Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [131, 5, 'Fees Discount Menu', 'fees-discount', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [132, 5, '➡ Create Fees Discount Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [133, 5, '➡ Fees Discount Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [134, 5, '➡ Fees Discount Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [135, 5, '➡ Fees Discount Assign', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [136, 5, 'Fees Carry Forward Menu', 'fees-forward', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            //  -- End Fees Collection Menu

            //  -- Start Accounts Menu

            [137, 6, 'Accounts Menu', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [138, 6, 'Profit Menu', 'profit', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [139, 6, 'Income Menu', 'add-income', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [140, 6, '➡ Create Income Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [141, 6, '➡ Income Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [142, 6, '➡ Income Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [143, 6, 'Expense Menu', 'add-expense', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [144, 6, '➡ Create Expense Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [145, 6, '➡ Expense Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [146, 6, '➡ Expense Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [147, 6, 'Search Menu', 'search-account', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [148, 6, 'Chart of Account Menu', 'chart-of-account', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [149, 6, '➡ Create Chart of Account Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [150, 6, '➡ Chart of Account Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [151, 6, '➡ Chart of Account Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [152, 6, 'Payment method Menu', 'payment-method', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [153, 6, '➡ Create Payment method Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [154, 6, '➡ Payment method Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [155, 6, '➡ Payment method Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [156, 6, 'Bank Account Menu', 'bank-account', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [157, 6, '➡ Create Bank Account Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [158, 6, '➡ Bank Account Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [159, 6, '➡ Bank Account Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            // -- End Accounts Menu

            // -- Start Human Resource Menu

            [160, 7, 'Human Resource Menu', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [161, 7, 'Staff Directory Menu', 'staff-directory', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [162, 7, '➡ Staff Directory Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [163, 7, '➡ Staff Directory Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [164, 7, '➡ Staff Directory Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [165, 7, 'Staff Attendance Menu', 'staff-attendance', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [166, 7, '➡ Staff Attendance Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [167, 7, '➡ Staff Attendance Edit', '', 0, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [168, 7, '➡ Staff Attendance Delete', '', 0, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [169, 7, 'Staff Attendance Report Menu', 'staff-attendance-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [170, 7, 'Payroll Menu', 'payroll', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [171, 7, '➡ Payroll Edit', '', 0, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [172, 7, '➡ Payroll Delete', '', 0, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [173, 7, '➡ Payroll Search', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [174, 7, '➡ Generate Payroll', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [175, 7, '➡ Payroll Create', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [176, 7, '➡ Payroll Proceed To Pay', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [177, 7, '➡ View Payslip', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [178, 7, 'Payroll Report Menu', 'payroll-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [179, 7, '➡ Payroll Report Search', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [180, 7, 'Designations Menu', 'designation', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [181, 7, '➡ Designations Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [182, 7, '➡ Designations Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [183, 7, '➡ Designations Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [184, 7, 'Departments Menu', 'department', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [185, 7, '➡ Departments Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [186, 7, '➡ Departments Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [187, 7, '➡ Departments Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            // -- End Human Resource Menu

            // -- Start examination Menu

            [188, 8, 'Leave Menu', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [189, 8, 'Approve Leave Menu', 'approve-leave', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [190, 8, '➡ Approve Leave Add', '', 0, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [191, 8, '➡ Approve Leave Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [192, 8, '➡ Approve Leave Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [193, 8, 'Apply Leave Menu', 'apply-leave', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [194, 8, '➡ Apply Leave View', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [195, 8, '➡ Apply Leave Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [196, 8, 'Pending Leave Menu', 'pending-leave', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [197, 8, '➡ Pending Leave View', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [198, 8, '➡ Pending Leave Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [199, 8, 'Leave Define Menu', 'leave-define', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [200, 8, '➡ Leave Define Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [201, 8, '➡ Leave Define Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [202, 8, '➡ Leave Define Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [203, 8, 'Leave Type Menu', 'leave-type', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [204, 8, '➡ Leave Type Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [205, 8, '➡ Leave Type Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [206, 8, '➡ Leave Type Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            // -- End Leave Menu

            // -- Start Examination Menu

            [207, 9, 'Examination Menu', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [208, 9, 'Add Exam Type Menu', 'exam-type', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [209, 9, '➡ Add Exam Type Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [210, 9, '➡ Add Exam Type Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [211, 9, '➡ Add Exam Type Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [212, 9, '➡ Exam Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [213, 9, '➡ Exam Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [214, 9, 'Exam Setup Menu', 'exam', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [215, 9, '➡ Exam Setup Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [216, 9, '➡ Exam Setup Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [217, 9, 'Exam Schedule Menu', 'exam-schedule', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [218, 9, '➡ Exam Schedule Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [219, 9, '➡ Exam Schedule Create', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [220, 9, 'Exam Attendance Menu', 'exam-attendance', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [221, 9, '➡ Exam Attendance Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [222, 9, 'Marks Register Menu', 'marks-register', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [223, 9, '➡ Marks Register Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [224, 9, '➡ Marks Register Create', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [225, 9, 'Marks Grade Menu', 'marks-grade', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [226, 9, '➡ Marks Grade Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [227, 9, '➡ Marks Grade Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [228, 9, '➡ Marks Grade Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [229, 9, 'Send Marks By SMS Menu', 'send-marks-by-sms', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [230, 9, 'Question Group Menu', 'question-group', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [231, 9, '➡ Question Group Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [232, 9, '➡ Question Group Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [233, 9, '➡ Question Group Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [234, 9, 'Question Bank Menu', 'question-bank', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [235, 9, '➡ Question Bank Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [236, 9, '➡ Question Bank Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [237, 9, '➡ Question Bank Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [238, 9, 'Online Exam Menu', 'online-exam', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [239, 9, '➡ Online Exam Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [240, 9, '➡ Online Exam Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [241, 9, '➡ Online Exam Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [242, 9, '➡ Online Exam Manage Question', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [243, 9, '➡ Online Exam Marks Register', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [244, 9, '➡ Online Exam Result', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            //  -- End  examination Menu

            //  -- Start  Academics Menu
            [245, 10, 'Academics Menu', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [246, 10, 'Class Routine Menu', 'class-routine-new', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [247, 10, '➡ Class Routine Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [248, 10, '➡ Class Routine Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [249, 10, '➡ Class Routine Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [250, 10, 'Assign Subject Menu', 'assign-subject', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [251, 10, '➡ Assign Subject Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [252, 10, '➡ Assign Subject Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [253, 10, 'Assign Class Teacher Menu', 'assign-class-teacher', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [254, 10, '➡ Assign Class Teacher Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [255, 10, '➡ Assign Class Teacher Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [256, 10, '➡ Assign Class Teacher Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [257, 10, 'Subjects Menu', 'subject', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [258, 10, '➡ Subjects Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [259, 10, '➡ Subjects Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [260, 10, '➡ Subjects Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [261, 10, 'Class Menu', 'class', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [262, 10, '➡ Class Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [263, 10, '➡ Class Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [264, 10, '➡ Class Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [265, 10, 'Section Menu', 'section', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [266, 10, '➡ Section Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [267, 10, '➡ Section Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [268, 10, '➡ Section Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [269, 10, 'Class Room Menu', 'class-room', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [270, 10, '➡ Class Room Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [271, 10, '➡ Class Room Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [272, 10, '➡ Class Room Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [273, 10, 'CL/EX Time Setup Menu', 'class-time', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [274, 10, '➡ CL/EX Time Setup Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [275, 10, '➡ CL/EX Time Setup Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [276, 10, '➡ CL/EX Time Setup Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            // -- End  Academics Menu
            // -- Start  Homework Menu

            [277, 11, 'Homework Menu', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [278, 11, 'Add Homework Menu', 'add-homeworks', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [279, 11, '➡ Create Homework Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [280, 11, 'Homework List Menu', 'homework-list', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [281, 11, '➡ Homework List Evaluation', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [282, 11, '➡ Homework List Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [283, 11, '➡ Homework List Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [284, 11, 'Homework Evaluation Report Menu', 'evaluation-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [285, 11, '➡ Homework Evaluation Report View', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            //  -- End  Homework Menu

            // -- Start  Communicate Menu

            [286, 12, 'Communicate Menu', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [287, 12, 'Notice Board Menu', 'notice-list', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [288, 12, '➡ Create Notice Board Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [289, 12, '➡ Create Notice Board Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [290, 12, '➡ Create Notice Board Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [291, 12, 'Send Email / SMS  Menu', 'send-email-sms-view', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [292, 12, '➡ Send Email / SMS  Send', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [293, 12, 'Email / SMS Log Menu', 'email-sms-log', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [294, 12, 'Event Menu', 'event', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [295, 12, '➡ Event Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [296, 12, '➡ Event Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [297, 12, '➡ Event Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            //  -- End  Communicate Menu

            // -- Start  Library Menu

            [298, 13, 'Library Menu', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [299, 13, 'Add Book Menu', 'add-book', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [300, 13, '➡ Create Add Book Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [301, 13, 'Book List  Menu', 'book-list', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [302, 13, '➡ Create Book List Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [303, 13, '➡ Create Book List Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [304, 13, 'Book Category Menu', 'book-category-list', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [305, 13, '➡ Book Category Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [306, 13, '➡ Book Category Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [307, 13, '➡ Book Category Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [308, 13, 'Add Member Menu', 'library-member', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [309, 13, '➡ Add Member Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [310, 13, '➡ Add Member Cancel', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [311, 13, 'Issue/Return Book Menu', 'member-list', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [312, 13, '➡ Issue/Return Book Issue', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [313, 13, '➡ Issue/Return Book Return', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [314, 13, 'All Issued Book', 'all-issed-book', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            //  -- End  Library Menu

            //  -- Start Invemtory Menu
            [315, 14, 'Inventory Menu', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [316, 14, 'Item Category Menu', 'item-category', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [317, 14, '➡ Create Item Category Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [318, 14, '➡ Create Item Category Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [319, 14, '➡ Item Category Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [320, 14, 'Item List Menu', 'item-list', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [321, 14, '➡ Create Item List Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [322, 14, '➡ Item List Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [323, 14, '➡ Item List Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [324, 14, 'Item Store Menu', 'item-store', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [325, 14, '➡ Create Item Store Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [326, 14, '➡ Item Store Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [327, 14, '➡ Item Store Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [328, 14, 'Supplier Menu', 'suppliers', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [329, 14, '➡ Create Supplier Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [330, 14, '➡ Supplier Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [331, 14, '➡ Supplier Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [332, 14, 'Item Receive Menu', 'item-receive', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [333, 14, '➡ Create Item Receive Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [334, 14, 'Item Receive List Menu', 'item-receive-list', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [335, 14, '➡ Create Item Receive List Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [336, 14, '➡ Item Receive List Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [337, 14, '➡ Item Receive List View', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [338, 14, '➡ Item Receive List Cancel', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [339, 14, 'Item Sell Menu', 'item-sell-list', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [340, 14, '➡ Create Item Sell Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [341, 14, '➡ Item Sell Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [342, 14, '➡ Item Sell Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [343, 14, '➡ Add Payment', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [344, 14, '➡ View Payment', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [345, 14, 'Item Issue Menu', 'item-issue', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [346, 14, '➡ Create Item Issue Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [347, 14, '➡ Item Issue Return', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            //  -- End  Inventory Menu

            //  -- Start Transport Menu
            [348, 15, 'Transport Menu', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [349, 15, 'Routes Menu', 'transport-route', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [350, 15, '➡ Create Routes Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [351, 15, '➡ Create Routes Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [352, 15, '➡ Create Routes Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [353, 15, 'Vehicle Menu', 'vehicle', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [354, 15, '➡ Create Vehicle Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [355, 15, '➡ Create Vehicle Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [356, 15, '➡ Create Vehicle Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [357, 15, 'Assign Vehicle Menu', 'assign-vehicle', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [358, 15, '➡ Create Assign Vehicle Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [359, 15, '➡ Create Assign Vehicle Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [360, 15, '➡ Create Assign Vehicle Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [361, 15, 'Student Transport Report Menu', 'student-transport-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            //  -- End  Transport Menu

            //  -- Start Dormitory Menu
            [362, 16, 'Dormitory Menu', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [363, 16, 'Dormitory Rooms Menu', 'room-list', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [364, 16, '➡ Create Dormitory Rooms Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [365, 16, '➡ Create Dormitory Rooms Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [366, 16, '➡ Create Dormitory Rooms Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [367, 16, 'Dormitory Menu', 'dormitory-list', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [368, 16, '➡ Create Dormitory Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [369, 16, '➡ Create Dormitory Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [370, 16, '➡ Create Dormitory Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [371, 16, 'Room Type Menu', 'room-type', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [372, 16, '➡ Create Room Type Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [373, 16, '➡ Create Room Type Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [374, 16, '➡ Create Room Type Delete', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [375, 16, 'Student Dormitory Report Menu', 'student-dormitory-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            //  -- End  Dormitory Menu

            //  -- Start  Reports Menu

            [376, 17, 'Reports Menu', 'student-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [377, 17, 'Guardian Report Menu', 'guardian-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [378, 17, 'Student History Menu', 'student-history', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [379, 17, 'Student Login Report', 'student-login-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [380, 17, '➡ Student Login Report Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [381, 17, 'Fees Statement Menu', 'fees-statement', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [382, 17, 'Balance Fees Report Menu', 'balance-fees-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [383, 17, 'Transaction Report Menu', 'transaction-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [384, 17, 'Class Report Menu', 'class-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [385, 17, 'Class Routine Menu', 'class-routine-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [386, 17, 'Exam Routine Menu', 'exam-routine-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [387, 17, 'Teacher Class Routine Menu', 'teacher-class-routine-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [388, 17, 'Merit List Report Menu', 'merit-list-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [389, 17, 'Online Exam Report Menu', 'online-exam-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [390, 17, 'Mark Sheet Report Menu', 'mark-sheet-report-student', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [391, 17, 'Tabulation Sheet Report Menu', 'tabulation-sheet-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [392, 17, 'Progress Card Report Menu', 'progress-card-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [393, 17, 'Student Fine Report Menu', 'student-fine-report', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],

            [394, 17, 'User Log Menu', 'user-log', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [395, 8, '➡ Apply Leave Add', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [396, 8, '➡ Apply Leave Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
            [397, 9, '➡ Exam Setup Edit', '', 1, 1, 1, 1, '2019-07-25 02:21:21', '2019-07-25 04:24:22'],
        ];

        foreach ($module_infos as $module_info) {
            $new = new SmModuleLink();
            $new->id = $module_info[0];
            $new->module_id = $module_info[1];
            $new->name = $module_info[2];
            $new->route = $module_info[3];
            $new->active_status = $module_info[4];
            $new->created_by = $module_info[5];
            $new->updated_by = $module_info[6];
            $new->school_id = $module_info[7];
            $new->created_at = $module_info[8];
            $new->save();
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sm_module_links');
    }
}
