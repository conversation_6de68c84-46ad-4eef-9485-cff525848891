<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateFeesInstallmentCreditsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('fees_installment_credits', function (Blueprint $blueprint): void {
            $blueprint->id();
            $blueprint->integer('student_id');
            $blueprint->integer('student_record_id');
            $blueprint->boolean('active_status')->default(1);
            $blueprint->integer('school_id');
            $blueprint->float('amount')->default(0);
            $blueprint->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('fees_installment_credits');
    }
}
