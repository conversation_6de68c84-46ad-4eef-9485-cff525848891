<?php

use App\SmModulePermissionAssign;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSmModulePermissionAssignsTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sm_module_permission_assigns', function (Blueprint $blueprint): void {
            $blueprint->increments('id');
            $blueprint->tinyInteger('active_status')->default(1);
            $blueprint->timestamps();

            $blueprint->integer('module_id')->nullable()->unsigned();
            $blueprint->foreign('module_id')->references('id')->on('sm_module_permissions')->onDelete('cascade');

            $blueprint->integer('role_id')->nullable()->unsigned();
            $blueprint->foreign('role_id')->references('id')->on('roles')->onDelete('cascade');

            $blueprint->integer('created_by')->nullable()->default(1)->unsigned();

            $blueprint->integer('updated_by')->nullable()->default(1)->unsigned();

            $blueprint->integer('school_id')->nullable()->default(1)->unsigned();
            $blueprint->foreign('school_id')->references('id')->on('sm_schools')->onDelete('cascade');
        });

        // for ($j = 1; $j <= 8; ++$j) {

        //     if ($j != 2 && $j != 3) {

        //         for ($i = 1; $i <= 21; ++$i) {

        //             $assign = new SmModulePermissionAssign();
        //             $assign->module_id = $i;
        //             $assign->role_id = $j;
        //             $assign->created_by = 1;
        //             $assign->updated_by = 1;
        //             $assign->save();
        //         }
        //     }
        // }

        // for ($i = 22; $i <= 35; ++$i) {

        //     $assign = new SmModulePermissionAssign();
        //     $assign->module_id = $i;
        //     $assign->role_id = 2;
        //     $assign->created_by = 1;
        //     $assign->updated_by = 1;
        //     $assign->save();
        // }

        // for ($i = 36; $i <= 46; ++$i) {

        //     $assign = new SmModulePermissionAssign();
        //     $assign->module_id = $i;
        //     $assign->role_id = 3;
        //     $assign->created_by = 1;
        //     $assign->updated_by = 1;
        //     $assign->save();
        // }

        // $sql = "INSERT INTO `sm_module_permission_assigns` (`id`, `active_status`, `created_at`, `updated_at`, `module_id`, `role_id`, `created_by`, `updated_by`, `school_id`) VALUES

        $datas = [
            [1, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 1, 1, 1, 1, 1],
            [2, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 2, 1, 1, 1, 1],
            [3, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 3, 1, 1, 1, 1],
            [4, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 4, 1, 1, 1, 1],
            [5, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 5, 1, 1, 1, 1],
            [6, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 6, 1, 1, 1, 1],
            [7, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 7, 1, 1, 1, 1],
            [8, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 8, 1, 1, 1, 1],
            [9, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 9, 1, 1, 1, 1],
            [10, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 10, 1, 1, 1, 1],
            [11, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 11, 1, 1, 1, 1],
            [12, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 12, 1, 1, 1, 1],
            [13, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 13, 1, 1, 1, 1],
            [14, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 14, 1, 1, 1, 1],
            [15, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 15, 1, 1, 1, 1],
            [16, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 16, 1, 1, 1, 1],
            [17, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 17, 1, 1, 1, 1],
            [18, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 18, 1, 1, 1, 1],
            [19, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 19, 1, 1, 1, 1],
            [20, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 20, 1, 1, 1, 1],
            [21, 1, '2019-11-18 08:14:09', '2019-11-18 08:14:09', 21, 1, 1, 1, 1],
            [127, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 22, 2, 1, 1, 1],
            [128, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 23, 2, 1, 1, 1],
            [129, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 24, 2, 1, 1, 1],
            [130, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 25, 2, 1, 1, 1],
            [131, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 26, 2, 1, 1, 1],
            [132, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 27, 2, 1, 1, 1],
            [133, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 28, 2, 1, 1, 1],
            [134, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 29, 2, 1, 1, 1],
            [135, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 30, 2, 1, 1, 1],
            [136, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 31, 2, 1, 1, 1],
            [137, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 32, 2, 1, 1, 1],
            [138, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 33, 2, 1, 1, 1],
            [139, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 34, 2, 1, 1, 1],
            [140, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 35, 2, 1, 1, 1],
            [141, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 36, 3, 1, 1, 1],
            [142, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 37, 3, 1, 1, 1],
            [143, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 38, 3, 1, 1, 1],
            [144, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 39, 3, 1, 1, 1],
            [145, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 40, 3, 1, 1, 1],
            [146, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 41, 3, 1, 1, 1],
            [147, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 42, 3, 1, 1, 1],
            [148, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 43, 3, 1, 1, 1],
            [149, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 44, 3, 1, 1, 1],
            [150, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 45, 3, 1, 1, 1],
            [151, 1, '2019-11-18 08:14:10', '2019-11-18 08:14:10', 46, 3, 1, 1, 1],
            [167, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 2, 5, 1, 1, 1],
            [168, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 3, 5, 1, 1, 1],
            [169, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 4, 5, 1, 1, 1],
            [170, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 5, 5, 1, 1, 1],
            [171, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 6, 5, 1, 1, 1],
            [172, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 7, 5, 1, 1, 1],
            [173, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 8, 5, 1, 1, 1],
            [174, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 9, 5, 1, 1, 1],
            [175, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 10, 5, 1, 1, 1],
            [176, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 11, 5, 1, 1, 1],
            [177, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 12, 5, 1, 1, 1],
            [178, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 13, 5, 1, 1, 1],
            [179, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 14, 5, 1, 1, 1],
            [180, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 15, 5, 1, 1, 1],
            [181, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 16, 5, 1, 1, 1],
            [182, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 17, 5, 1, 1, 1],
            [183, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 19, 5, 1, 1, 1],
            [184, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 20, 5, 1, 1, 1],
            [185, 1, '2019-12-03 10:55:49', '2019-12-03 10:55:49', 21, 5, 1, 1, 1],
            [197, 1, '2019-12-03 11:14:06', '2019-12-03 11:14:06', 3, 4, 1, 1, 1],
            [198, 1, '2019-12-03 11:14:06', '2019-12-03 11:14:06', 4, 4, 1, 1, 1],
            [199, 1, '2019-12-03 11:14:06', '2019-12-03 11:14:06', 8, 4, 1, 1, 1],
            [200, 1, '2019-12-03 11:14:06', '2019-12-03 11:14:06', 9, 4, 1, 1, 1],
            [201, 1, '2019-12-03 11:14:06', '2019-12-03 11:14:06', 10, 4, 1, 1, 1],
            [202, 1, '2019-12-03 11:14:06', '2019-12-03 11:14:06', 11, 4, 1, 1, 1],
            [203, 1, '2019-12-03 11:14:06', '2019-12-03 11:14:06', 17, 4, 1, 1, 1],
            [204, 1, '2019-12-03 11:14:06', '2019-12-03 11:14:06', 19, 4, 1, 1, 1],
            [205, 1, '2019-12-03 11:16:38', '2019-12-03 11:16:38', 3, 6, 1, 1, 1],
            [206, 1, '2019-12-03 11:16:38', '2019-12-03 11:16:38', 5, 6, 1, 1, 1],
            [207, 1, '2019-12-03 11:16:38', '2019-12-03 11:16:38', 6, 6, 1, 1, 1],
            [208, 1, '2019-12-03 11:16:38', '2019-12-03 11:16:38', 7, 6, 1, 1, 1],
            [209, 1, '2019-12-03 11:16:38', '2019-12-03 11:16:38', 14, 6, 1, 1, 1],
            [210, 1, '2019-12-03 11:16:38', '2019-12-03 11:16:38', 17, 6, 1, 1, 1],
            [211, 1, '2019-12-03 11:17:09', '2019-12-03 11:17:09', 2, 7, 1, 1, 1],
            [212, 1, '2019-12-03 11:17:09', '2019-12-03 11:17:09', 3, 7, 1, 1, 1],
            [213, 1, '2019-12-03 11:17:09', '2019-12-03 11:17:09', 7, 7, 1, 1, 1],
            [214, 1, '2019-12-03 11:17:30', '2019-12-03 11:17:30', 3, 8, 1, 1, 1],
            [215, 1, '2019-12-03 11:17:30', '2019-12-03 11:17:30', 7, 8, 1, 1, 1],
            [216, 1, '2019-12-03 11:17:30', '2019-12-03 11:17:30', 13, 8, 1, 1, 1],
        ];

        foreach ($datas as $data) {
            $new = new SmModulePermissionAssign();
            $new->id = $data[0];
            $new->active_status = $data[1];
            $new->created_at = $data[2];
            $new->updated_at = $data[3];
            $new->module_id = $data[4];
            $new->role_id = $data[5];
            $new->created_by = $data[6];
            $new->updated_by = $data[7];
            $new->school_id = $data[8];
            $new->save();
        }

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sm_module_permission_assigns');
    }
}
