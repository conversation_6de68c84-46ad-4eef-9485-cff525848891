<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSmSuppliersTable extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('sm_suppliers', function (Blueprint $blueprint): void {
            $blueprint->increments('id');
            $blueprint->string('company_name', 100)->nullable();
            $blueprint->string('company_address', 500)->nullable();
            $blueprint->string('contact_person_name')->nullable();
            $blueprint->string('contact_person_mobile')->nullable();
            $blueprint->string('contact_person_email', 100)->nullable();
            $blueprint->string('cotact_person_address', 500)->nullable();
            $blueprint->string('description', 500)->nullable();
            $blueprint->tinyInteger('active_status')->default(1);
            $blueprint->timestamps();

            $blueprint->integer('created_by')->nullable()->default(1)->unsigned();

            $blueprint->integer('updated_by')->nullable()->default(1)->unsigned();

            $blueprint->integer('school_id')->nullable()->default(1)->unsigned();
            // $table->foreign('school_id')->references('id')->on('sm_schools')->onDelete('cascade');

            $blueprint->integer('academic_id')->nullable()->default(1)->unsigned();
            $blueprint->foreign('academic_id')->references('id')->on('sm_academic_years')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sm_suppliers');
    }
}
