<?php

use Illuminate\Database\Migrations\Migration;

class AddTeacherEvaluationSidebarmenu extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $teacher_evaluation = [
            'teacher-evaluation' => [
                'module' => null,
                'sidebar_menu' => 'teacher-evaluation',
                'name' => 'Teacher Evaluation',
                'lang_name' => 'teacherEvaluation.teacher_evaluation',
                'icon' => 'fas fa-star',
                'svg' => null,
                'route' => 'teacher-evaluation',
                'parent_route' => null,
                'is_admin' => 1,
                'is_teacher' => 1,
                'is_student' => 0,
                'is_parent' => 0,
                'position' => 13,
                'is_saas' => 0,
                'is_menu' => 1,
                'status' => 1,
                'menu_status' => 1,
                'relate_to_child' => 0,
                'alternate_module' => null,
                'permission_section' => 0,
                'user_id' => null,
                'type' => 1,
                'old_id' => null,
                'child' => [
                    'teacher-approved-evaluation-report' => [
                        'module' => null,
                        'sidebar_menu' => null,
                        'name' => 'Approved Report',
                        'lang_name' => 'teacherEvaluation.approved_report',
                        'icon' => null,
                        'svg' => null,
                        'route' => 'teacher-approved-evaluation-report',
                        'parent_route' => 'teacher-evaluation',
                        'is_admin' => 1,
                        'is_teacher' => 0,
                        'is_student' => 0,
                        'is_parent' => 0,
                        'position' => 1,
                        'is_saas' => 0,
                        'is_menu' => 1,
                        'status' => 1,
                        'menu_status' => 1,
                        'relate_to_child' => 0,
                        'alternate_module' => null,
                        'permission_section' => 0,
                        'user_id' => null,
                        'type' => 2,
                        'old_id' => null,
                        'child' => [
                            'teacher-evaluation-approve-delete' => [
                                'module' => null,
                                'sidebar_menu' => null,
                                'name' => 'Delete',
                                'lang_name' => null,
                                'icon' => null,
                                'svg' => null,
                                'route' => 'teacher-evaluation-approve-delete',
                                'parent_route' => 'teacher-approved-evaluation-report',
                                'is_admin' => 1,
                                'is_teacher' => 0,
                                'is_student' => 0,
                                'is_parent' => 0,
                                'position' => 1,
                                'is_saas' => 0,
                                'is_menu' => 0,
                                'status' => 1,
                                'menu_status' => 1,
                                'relate_to_child' => 0,
                                'alternate_module' => null,
                                'permission_section' => 0,
                                'user_id' => null,
                                'type' => 3,
                                'old_id' => null,
                            ],
                        ],
                    ],
                    'teacher-pending-evaluation-report' => [
                        'module' => null,
                        'sidebar_menu' => null,
                        'name' => 'Pending Report',
                        'lang_name' => 'teacherEvaluation.pending_report',
                        'icon' => null,
                        'svg' => null,
                        'route' => 'teacher-pending-evaluation-report',
                        'parent_route' => 'teacher-evaluation',
                        'is_admin' => 1,
                        'is_teacher' => 0,
                        'is_student' => 0,
                        'is_parent' => 0,
                        'position' => 2,
                        'is_saas' => 0,
                        'is_menu' => 1,
                        'status' => 1,
                        'menu_status' => 1,
                        'relate_to_child' => 0,
                        'alternate_module' => null,
                        'permission_section' => 0,
                        'user_id' => null,
                        'type' => 2,
                        'old_id' => null,
                        'child' => [
                            'teacher-evaluation-approve-submit' => [
                                'module' => null,
                                'sidebar_menu' => null,
                                'name' => 'Add',
                                'lang_name' => null,
                                'icon' => null,
                                'svg' => null,
                                'route' => 'teacher-evaluation-approve-submit',
                                'parent_route' => 'teacher-pending-evaluation-report',
                                'is_admin' => 1,
                                'is_teacher' => 0,
                                'is_student' => 0,
                                'is_parent' => 0,
                                'position' => 1,
                                'is_saas' => 0,
                                'is_menu' => 0,
                                'status' => 1,
                                'menu_status' => 1,
                                'relate_to_child' => 0,
                                'alternate_module' => null,
                                'permission_section' => 0,
                                'user_id' => null,
                                'type' => 3,
                                'old_id' => null,
                            ],
                            'teacher-evaluation-approve-delete' => [
                                'module' => null,
                                'sidebar_menu' => null,
                                'name' => 'Delete',
                                'lang_name' => null,
                                'icon' => null,
                                'svg' => null,
                                'route' => 'teacher-evaluation-approve-delete',
                                'parent_route' => 'teacher-pending-evaluation-report',
                                'is_admin' => 1,
                                'is_teacher' => 0,
                                'is_student' => 0,
                                'is_parent' => 0,
                                'position' => 2,
                                'is_saas' => 0,
                                'is_menu' => 0,
                                'status' => 1,
                                'menu_status' => 1,
                                'relate_to_child' => 0,
                                'alternate_module' => null,
                                'permission_section' => 0,
                                'user_id' => null,
                                'type' => 3,
                                'old_id' => null,
                            ],
                        ],
                    ],
                    'teacher-wise-evaluation-report' => [
                        'module' => null,
                        'sidebar_menu' => null,
                        'name' => 'Teacher Wise Report',
                        'lang_name' => 'teacherEvaluation.teacher_wise_report',
                        'icon' => null,
                        'svg' => null,
                        'route' => 'teacher-wise-evaluation-report',
                        'parent_route' => 'teacher-evaluation',
                        'is_admin' => 1,
                        'is_teacher' => 0,
                        'is_student' => 0,
                        'is_parent' => 0,
                        'position' => 3,
                        'is_saas' => 0,
                        'is_menu' => 1,
                        'status' => 1,
                        'menu_status' => 1,
                        'relate_to_child' => 0,
                        'alternate_module' => null,
                        'permission_section' => 0,
                        'user_id' => null,
                        'type' => 2,
                        'old_id' => null,
                        'child' => [
                            'teacher-evaluation-approve-delete' => [
                                'module' => null,
                                'sidebar_menu' => null,
                                'name' => 'Delete',
                                'lang_name' => null,
                                'icon' => null,
                                'svg' => null,
                                'route' => 'teacher-evaluation-approve-delete',
                                'parent_route' => 'teacher-wise-evaluation-report',
                                'is_admin' => 1,
                                'is_teacher' => 0,
                                'is_student' => 0,
                                'is_parent' => 0,
                                'position' => 1,
                                'is_saas' => 0,
                                'is_menu' => 0,
                                'status' => 1,
                                'menu_status' => 1,
                                'relate_to_child' => 0,
                                'alternate_module' => null,
                                'permission_section' => 0,
                                'user_id' => null,
                                'type' => 3,
                                'old_id' => null,
                            ],
                        ],
                    ],
                    'teacher-evaluation-setting' => [
                        'module' => null,
                        'sidebar_menu' => null,
                        'name' => 'Setting',
                        'lang_name' => 'teacherEvaluation.settings',
                        'icon' => null,
                        'svg' => null,
                        'route' => 'teacher-evaluation-setting',
                        'parent_route' => 'teacher-evaluation',
                        'is_admin' => 1,
                        'is_teacher' => 0,
                        'is_student' => 0,
                        'is_parent' => 0,
                        'position' => 4,
                        'is_saas' => 0,
                        'is_menu' => 1,
                        'status' => 1,
                        'menu_status' => 1,
                        'relate_to_child' => 0,
                        'alternate_module' => null,
                        'permission_section' => 0,
                        'user_id' => null,
                        'type' => 2,
                        'old_id' => null,
                        'child' => [
                            'teacher-evaluation-setting-update' => [
                                'module' => null,
                                'sidebar_menu' => null,
                                'name' => 'Edit',
                                'lang_name' => null,
                                'icon' => null,
                                'svg' => null,
                                'route' => 'teacher-evaluation-setting-update',
                                'parent_route' => 'teacher-evaluation-setting',
                                'is_admin' => 1,
                                'is_teacher' => 0,
                                'is_student' => 0,
                                'is_parent' => 0,
                                'position' => 1,
                                'is_saas' => 0,
                                'is_menu' => 0,
                                'status' => 1,
                                'menu_status' => 1,
                                'relate_to_child' => 0,
                                'alternate_module' => null,
                                'permission_section' => 0,
                                'user_id' => null,
                                'type' => 3,
                                'old_id' => null,
                            ],
                        ],
                    ],
                    'teacher-panel-evaluation-report' => [
                        'module' => null,
                        'sidebar_menu' => null,
                        'name' => 'My Report',
                        'lang_name' => 'teacherEvaluation.my_report',
                        'icon' => null,
                        'svg' => null,
                        'route' => 'teacher-panel-evaluation-report',
                        'parent_route' => 'teacher-evaluation',
                        'is_admin' => 0,
                        'is_teacher' => 1,
                        'is_student' => 0,
                        'is_parent' => 0,
                        'position' => 5,
                        'is_saas' => 0,
                        'is_menu' => 1,
                        'status' => 1,
                        'menu_status' => 1,
                        'relate_to_child' => 0,
                        'alternate_module' => null,
                        'permission_section' => 0,
                        'user_id' => null,
                        'type' => 2,
                        'old_id' => null,
                    ],
                ],
            ],
        ];
        foreach ($teacher_evaluation as $data) {
            storePermissionData($data);
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
}
