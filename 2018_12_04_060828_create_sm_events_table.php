<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSmEventsTable extends Migration
{
    public function up(): void
    {
        Schema::create('sm_events', function (Blueprint $blueprint): void {
            $blueprint->increments('id');
            $blueprint->string('event_title', 200)->nullable();
            $blueprint->string('for_whom', 200)->nullable()->comment('teacher, student, parents, all');
            $blueprint->text('role_ids')->nullable();
            $blueprint->text('url')->nullable();
            $blueprint->string('event_location', 200)->nullable();
            $blueprint->string('event_des', 500)->nullable();
            $blueprint->date('from_date')->nullable();
            $blueprint->date('to_date')->nullable();
            $blueprint->string('uplad_image_file', 200)->nullable();
            $blueprint->tinyInteger('active_status')->default(1);
            $blueprint->timestamps();

            $blueprint->integer('created_by')->nullable()->default(1)->unsigned();
            $blueprint->integer('updated_by')->nullable()->default(1)->unsigned();

            $blueprint->integer('school_id')->nullable()->default(1)->unsigned();
            $blueprint->foreign('school_id')->references('id')->on('sm_schools')->onDelete('cascade');

            $blueprint->integer('academic_id')->nullable()->default(1)->unsigned();
            $blueprint->foreign('academic_id')->references('id')->on('sm_academic_years')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('sm_events');
    }
}
